version: '3.8'

services:
  # MCP Server Process Report
  mcp-server-process-report:
    image: mcp-server-process-report:v1
    container_name: mcp-server-process-report
    restart: unless-stopped
    ports:
      - "9001:9000"
    environment:
      - MCP_PLATFORM_BASEURL=https://dstaff.dbappsecurity.com.cn
      - MCP_AUTH_TOKEN=
      - MCP_OPENAI__API_KEY=example
      - MCP_OPENAI__BASE_URL=https://ai.gitee.com/v1
      - MCP_OPENAI__MODEL=Qwen3-4B
    expose:
      - "9000"
    networks:
      - dstaff-teacher-mcp-network

  # MCP Server RedTeam
  mcp-server-redteam:
    image: mcp-server-redteam:v1
    container_name: mcp-server-redteam
    restart: unless-stopped
    ports:
      - "9000:9000"
    expose:
      - "9000"
    volumes:
      - redteam-config:/app/config/.secrets.yaml:ro
    networks:
      - dstaff-teacher-mcp-network

  # MCP Server Code Audit
  mcp-server-codeaudit:
    image: mcp-server-codeaudit:v1
    container_name: mcp-server-codeaudit
    restart: unless-stopped
    ports:
      - "9002:9000"
    environment:
      - MCP_PLATFORM_BASEURL=https://dstaff.dbappsecurity.com.cn
      - MCP_WOODPECKER_SAST__BASEURL=https://10.50.67.10:10030/
      - MCP_WOODPECKER_SAST__AUTH_TOKEN=f9f4a533985343999e5018cd8908cac0
      - MCP_APPINSIGHT__BASEURL=http://10.50.67.10:10031/
      - MCP_APPINSIGHT__AUTH_TOKEN=gDC6F1JTWQ4k8yqU
      - MCP_DASAI__BASEURL=https://www.das-ai.com/
      - MCP_DASAI__APP_KEY=hengnaoRM28fix6n2TwohMcbDvK
      - MCP_DASAI__APP_SECRET=fbzvd4grxyhe8qdvzmtcd6e0pyb4iedc
      - MCP_DASAI__APP_KEY_BJ=hengnaoz4lVh6BlSyeTsR0tb7J7
      - MCP_DASAI__APP_SECRET_BJ=zxzuy2262cxtlrexzvux56tumkja1xck
      - MCP_DASSCA__BASEURL=http://10.50.67.10:10032/
      - MCP_DASSCA__USERNAME=admin
      - MCP_DASSCA__PASSWORD=R4eOahwjzYVVowM
    expose:
      - "9000"
    networks:
      - dstaff-teacher-mcp-network

  # Load Balancer API
  lb-api:
    image: k8s/lb-api:v3.0.1-dev
    container_name: lb-api
    restart: unless-stopped
    networks:
      - dstaff-teacher-mcp-network

networks:
  dstaff-teacher-mcp-network:
    driver: bridge
    name: dstaff-teacher-mcp-network

volumes:
  redteam-config:
    driver: local