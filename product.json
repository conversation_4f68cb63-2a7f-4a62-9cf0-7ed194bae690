{"version": "V1.0", "name": "dstaff-teacher-mcp", "type": "DEFAULT", "base_image": [], "service": {"app": [{"name": "mcp-server-process-report", "version": "v1", "build": {"image": "mcp-server-process-report:v1", "externalPortList": [{"externalPort": 9001, "internalPort": 9000}], "values": {"replicas": 1, "restartPolicy": "unless-stopped", "expose": [9000], "networks": ["dstaff-teacher-mcp-network"], "env": [{"name": "MCP_PLATFORM_BASEURL", "value": "https://dstaff.dbappsecurity.com.cn"}, {"name": "MCP_AUTH_TOKEN", "value": ""}, {"name": "MCP_OPENAI__API_KEY", "value": "example"}, {"name": "MCP_OPENAI__BASE_URL", "value": "https://ai.gitee.com/v1"}, {"name": "MCP_OPENAI__MODEL", "value": "Qwen3-4B"}]}}}, {"name": "mcp-server-redteam", "version": "v1", "build": {"image": "mcp-server-redteam:v1", "externalPortList": [{"externalPort": 9000, "internalPort": 9000}], "values": {"replicas": 1, "restartPolicy": "unless-stopped", "expose": [9000], "networks": ["dstaff-teacher-mcp-network"], "volumes": [{"name": "redteam-config", "configMap": "redteam-config", "mountPath": "/app/config/.secrets.yaml", "subPath": ".secrets.yaml", "readOnly": true}]}}}, {"name": "mcp-server-codeaudit", "version": "v1", "build": {"image": "mcp-server-codeaudit:v1", "externalPortList": [{"externalPort": 9002, "internalPort": 9000}], "values": {"replicas": 1, "restartPolicy": "unless-stopped", "expose": [9000], "networks": ["dstaff-teacher-mcp-network"], "env": [{"name": "MCP_PLATFORM_BASEURL", "value": "https://dstaff.dbappsecurity.com.cn"}, {"name": "MCP_WOODPECKER_SAST__BASEURL", "value": "https://10.50.67.10:10030/"}, {"name": "MCP_WOODPECKER_SAST__AUTH_TOKEN", "value": "f9f4a533985343999e5018cd8908cac0"}, {"name": "MCP_APPINSIGHT__BASEURL", "value": "http://10.50.67.10:10031/"}, {"name": "MCP_APPINSIGHT__AUTH_TOKEN", "value": "gDC6F1JTWQ4k8yqU"}, {"name": "MCP_DASAI__BASEURL", "value": "https://www.das-ai.com/"}, {"name": "MCP_DASAI__APP_KEY", "value": "hengnaoRM28fix6n2TwohMcbDvK"}, {"name": "MCP_DASAI__APP_SECRET", "value": "fbzvd4grxyhe8qdvzmtcd6e0pyb4iedc"}, {"name": "MCP_DASAI__APP_KEY_BJ", "value": "hengnaoz4lVh6BlSyeTsR0tb7J7"}, {"name": "MCP_DASAI__APP_SECRET_BJ", "value": "zxzuy2262cxtlrexzvux56tumkja1xck"}, {"name": "MCP_DASSCA__BASEURL", "value": "http://10.50.67.10:10032/"}, {"name": "MCP_DASSCA__USERNAME", "value": "admin"}, {"name": "MCP_DASSCA__PASSWORD", "value": "R4eOahwjzYVVowM"}]}}}, {"name": "lb-api", "version": "v3.0.1-dev", "build": {"image": "k8s/lb-api:v3.0.1-dev", "values": {"replicas": 1, "restartPolicy": "unless-stopped", "networks": ["dstaff-teacher-mcp-network"]}}}], "common": [], "base": [{"name": "kubesphere", "version": "v3.4.1"}, {"name": "minio", "version": "RELEASE.2024-08-03T04-33-23Z"}], "extend": []}, "infrastructure": {"networks": [{"name": "dstaff-teacher-mcp-network", "driver": "bridge"}], "volumes": [{"name": "redteam-config", "driver": "local"}]}}